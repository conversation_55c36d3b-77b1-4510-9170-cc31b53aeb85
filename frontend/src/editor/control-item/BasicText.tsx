import FormatAlignCenterIcon from "@mui/icons-material/FormatAlignCenter";
import FormatAlignLeftIcon from "@mui/icons-material/FormatAlignLeft";
import FormatAlignRightIcon from "@mui/icons-material/FormatAlignRight";
import FormatBoldIcon from "@mui/icons-material/FormatBold";
import FormatItalicIcon from "@mui/icons-material/FormatItalic";
import FormatUnderlinedIcon from "@mui/icons-material/FormatUnderlined";
import StrikethroughSIcon from "@mui/icons-material/StrikethroughS";
import {
  Box,
  Divider,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Tab,
  Tabs,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useState,
  useCallback,
  useMemo,
  useRef,
  useEffect,
} from "react";
import ColorPicker from "../components/color/ColorPicker";
import { GradientPicker } from "../components/color/GradientPicker";
import BaseSetting from "./BaseSetting";
import { StoreContext } from "../../store";
import { EditorElement, TextEditorElement } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";
import { styled } from "@mui/material/styles";
import SliderWithInput from "./SliderWithInput";

// 常量定义
const FONT_FAMILIES = [
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Courier",
  "Verdana",
  "Georgia",
  "Palatino",
  "Garamond",
  "Bookman",
  "Comic Sans MS",
  "Trebuchet MS",
  "Arial Black",
  "Impact",
  "Roboto",
] as const;

const DEFAULT_TEXT_STYLE = {
  fontSize: 100,
  textAlign: "left" as "left" | "center" | "right",
  styles: [] as string[],
  charSpacing: 0,
  lineHeight: 1,
  fontColor: "#ffffff",
  fontFamily: "Arial",
  strokeWidth: 0,
  strokeColor: "#000000",
  shadowColor: "#000000",
  shadowBlur: 0,
  shadowOffsetX: 0,
  shadowOffsetY: 0,
  gradientColors: ["#ffffff", "#000000"],
  useGradient: false,
  backgroundColor: "transparent",
};

// 样式组件
const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.8rem",
  fontWeight: theme.typography.fontWeightRegular,
  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

const StyledSelect = styled(Select)({
  "& .MuiSelect-select": {
    fontSize: "0.9rem",
  },
});

const StyledMenuItem = styled(MenuItem)({
  fontSize: "0.9rem",
  display: "flex",
  alignItems: "center",
  "&:hover": {
    backgroundColor: "#f0f0f0",
  },
});

const FontPreview = styled("span")({
  marginRight: "8px",
  fontSize: "0.9rem",
});

// 类型定义
type TextStyleState = typeof DEFAULT_TEXT_STYLE;

interface BasicTextProps {
  element: EditorElement | null;
}

const BasicText = ({ element }: BasicTextProps) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  const [textStyle, setTextStyle] =
    useState<TextStyleState>(DEFAULT_TEXT_STYLE);
  const [activeTab, setActiveTab] = useState(0);

  // 添加滚动位置状态和引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<number>(0);

  // 获取当前文本元素
  const textElement = useMemo(() => {
    if (!element || element.type !== "text") return null;
    return element as TextEditorElement;
  }, [element]);

  // 保存滚动位置
  const saveScrollPosition = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollPositionRef.current = scrollContainerRef.current.scrollTop;
    }
  }, []);

  // 恢复滚动位置
  const restoreScrollPosition = useCallback(() => {
    if (scrollContainerRef.current && scrollPositionRef.current > 0) {
      requestAnimationFrame(() => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollTop = scrollPositionRef.current;
        }
      });
    }
  }, []);

  // 监听滚动事件并保存位置
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      const handleScroll = () => saveScrollPosition();
      scrollContainer.addEventListener("scroll", handleScroll, {
        passive: true,
      });
      return () => scrollContainer.removeEventListener("scroll", handleScroll);
    }
  }, [saveScrollPosition]);

  // 在组件更新后恢复滚动位置
  useEffect(() => {
    restoreScrollPosition();
  });

  // 通用的样式更新函数
  const updateTextStyleAndStore = useCallback(
    (updates: Partial<TextStyleState>) => {
      if (!textElement) return;

      // 在更新状态前保存滚动位置
      saveScrollPosition();

      setTextStyle((prev) => ({ ...prev, ...updates }));
      store.updateTextStyle(textElement.id, updates);
    },
    [textElement, store, saveScrollPosition]
  );

  // 同步状态
  React.useEffect(() => {
    if (textElement?.properties) {
      const props = textElement.properties;
      setTextStyle({
        fontSize: props.fontSize || DEFAULT_TEXT_STYLE.fontSize,
        textAlign: props.textAlign || DEFAULT_TEXT_STYLE.textAlign,
        styles: props.styles || DEFAULT_TEXT_STYLE.styles,
        charSpacing: props.charSpacing || DEFAULT_TEXT_STYLE.charSpacing,
        lineHeight: props.lineHeight || DEFAULT_TEXT_STYLE.lineHeight,
        fontColor: props.fontColor || DEFAULT_TEXT_STYLE.fontColor,
        fontFamily: props.fontFamily || DEFAULT_TEXT_STYLE.fontFamily,
        strokeWidth: props.strokeWidth || DEFAULT_TEXT_STYLE.strokeWidth,
        strokeColor: props.strokeColor || DEFAULT_TEXT_STYLE.strokeColor,
        shadowColor: props.shadowColor || DEFAULT_TEXT_STYLE.shadowColor,
        shadowBlur: props.shadowBlur || DEFAULT_TEXT_STYLE.shadowBlur,
        shadowOffsetX: props.shadowOffsetX || DEFAULT_TEXT_STYLE.shadowOffsetX,
        shadowOffsetY: props.shadowOffsetY || DEFAULT_TEXT_STYLE.shadowOffsetY,
        gradientColors:
          props.gradientColors || DEFAULT_TEXT_STYLE.gradientColors,
        useGradient: props.useGradient || DEFAULT_TEXT_STYLE.useGradient,
        backgroundColor:
          props.backgroundColor || DEFAULT_TEXT_STYLE.backgroundColor,
      });
    }
  }, [textElement]);

  // 事件处理函数
  const handleTabChange = useCallback(
    (event: React.SyntheticEvent, newValue: number) => {
      setActiveTab(newValue);
    },
    []
  );

  const handleAlignmentChange = useCallback(
    (event: React.MouseEvent<HTMLElement>, newAlignment: string | null) => {
      if (newAlignment !== null) {
        updateTextStyleAndStore({ textAlign: newAlignment as any });
      }
    },
    [updateTextStyleAndStore]
  );

  const handleStyleChange = useCallback(
    (event: React.MouseEvent<HTMLElement>, newStyles: string[]) => {
      updateTextStyleAndStore({ styles: newStyles });
    },
    [updateTextStyleAndStore]
  );

  const handleColorChange = useCallback(
    (color: string) => {
      if (textStyle.useGradient) {
        const newGradientColors = [...textStyle.gradientColors];
        newGradientColors[0] = color;
        updateTextStyleAndStore({
          gradientColors: newGradientColors,
          useGradient: true,
        });
      } else {
        updateTextStyleAndStore({
          fontColor: color,
          useGradient: false,
        });
      }
    },
    [textStyle.useGradient, textStyle.gradientColors, updateTextStyleAndStore]
  );

  const handleGradientChange = useCallback(
    (newGradientColors: string[]) => {
      updateTextStyleAndStore({
        gradientColors: newGradientColors,
        useGradient: true,
      });
    },
    [updateTextStyleAndStore]
  );

  const handleFontFamilyChange = useCallback(
    (event: SelectChangeEvent<unknown>) => {
      const newFontFamily = event.target.value as string;
      updateTextStyleAndStore({ fontFamily: newFontFamily });
    },
    [updateTextStyleAndStore]
  );

  // 滑块组件配置
  const sliderConfigs = useMemo(
    () => [
      {
        key: "fontSize",
        label: t("font_size"),
        value: textStyle.fontSize,
        min: 0,
        max: 200,
        step: 1,
      },
      {
        key: "charSpacing",
        label: t("char_spacing"),
        value: textStyle.charSpacing,
        min: -200,
        max: 800,
        step: 0.1,
      },
      {
        key: "lineHeight",
        label: t("line_height"),
        value: textStyle.lineHeight,
        min: -2,
        max: 2,
        step: 0.01,
      },
    ],
    [textStyle.fontSize, textStyle.charSpacing, textStyle.lineHeight, t]
  );

  const advancedSliderConfigs = useMemo(
    () => [
      {
        key: "strokeWidth",
        label: t("stroke_width"),
        value: textStyle.strokeWidth,
        min: 0,
        max: 5,
        step: 0.2,
      },
      {
        key: "shadowBlur",
        label: t("shadow_blur"),
        value: textStyle.shadowBlur,
        min: 0,
        max: 10,
        step: 1,
      },
      {
        key: "shadowOffsetX",
        label: t("shadow_offset_x"),
        value: textStyle.shadowOffsetX,
        min: -50,
        max: 50,
        step: 1,
      },
      {
        key: "shadowOffsetY",
        label: t("shadow_offset_y"),
        value: textStyle.shadowOffsetY,
        min: -20,
        max: 20,
        step: 1,
      },
    ],
    [
      textStyle.strokeWidth,
      textStyle.shadowBlur,
      textStyle.shadowOffsetX,
      textStyle.shadowOffsetY,
      t,
    ]
  );

  // 早期返回检查
  if (!textElement) {
    return <></>;
  }

  // 渲染颜色选择器行
  const renderColorRow = (
    label: string,
    color: string,
    onChange: (color: string) => void,
    isGradient = false
  ) => (
    <Stack
      direction="row"
      spacing={2}
      alignItems="center"
      justifyContent="space-between"
    >
      <Typography
        variant="body2"
        sx={{
          color: "text.secondary",
        }}
      >
        {label}
      </Typography>
      {isGradient ? (
        <GradientPicker
          colors={textStyle.gradientColors}
          onChange={handleGradientChange}
        />
      ) : (
        <ColorPicker color={color} onChange={onChange} />
      )}
    </Stack>
  );

  // 渲染切换按钮组
  const renderToggleButtonGroup = (
    label: string,
    value: any,
    onChange: any,
    buttons: Array<{ value: string; icon: React.ReactNode; label: string }>
  ) => (
    <Stack direction="row" justifyContent="space-between" alignItems="center">
      <Typography
        variant="body2"
        sx={{
          color: "text.secondary",
        }}
      >
        {label}
      </Typography>
      <ToggleButtonGroup
        value={value}
        exclusive={buttons.length <= 3} // 对齐按钮使用 exclusive
        onChange={onChange}
        size="small"
      >
        {buttons.map((button) => (
          <ToggleButton
            key={button.value}
            value={button.value}
            aria-label={button.label}
            size="small"
          >
            {button.icon}
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
    </Stack>
  );

  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          {t("text")}
        </Typography>
      </Box>

      <StyledTabs value={activeTab} onChange={handleTabChange}>
        <StyledTab label={t("basic")} />
        <StyledTab label={t("advanced")} />
      </StyledTabs>

      <Box
        ref={scrollContainerRef}
        sx={{
          m: 2,
          width: "250px",
          height: "100%",
          overflow: "auto",
          pr: 2,
          "&::-webkit-scrollbar": {
            width: "1px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(255, 255, 255, 0.2)",
            borderRadius: "1px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.3)",
            },
          },
        }}
      >
        <Stack spacing={2}>
          {activeTab === 0 && (
            <>
              {/* 字体选择 */}
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  {t("font_family")}
                </Typography>
                <StyledSelect
                  value={textStyle.fontFamily}
                  onChange={handleFontFamilyChange}
                  sx={{ width: "50%" }}
                  size="small"
                >
                  {FONT_FAMILIES.map((font) => (
                    <StyledMenuItem key={font} value={font}>
                      <FontPreview style={{ fontFamily: font }}>
                        {font}
                      </FontPreview>
                    </StyledMenuItem>
                  ))}
                </StyledSelect>
              </Stack>

              {/* 字体颜色 */}
              {renderColorRow(
                t("font_color"),
                textStyle.fontColor,
                handleColorChange,
                textStyle.useGradient
              )}

              {/* 背景颜色 */}
              {renderColorRow(
                t("background"),
                textStyle.backgroundColor,
                (color) => updateTextStyleAndStore({ backgroundColor: color })
              )}

              {/* 滑块控件 */}
              {sliderConfigs.map((config) => (
                <SliderWithInput
                  key={config.key}
                  label={config.label}
                  value={config.value}
                  onChange={(newValue) =>
                    setTextStyle((prev) => ({
                      ...prev,
                      [config.key]: newValue,
                    }))
                  }
                  onChangeCommitted={(newValue) =>
                    updateTextStyleAndStore({ [config.key]: newValue })
                  }
                  min={config.min}
                  max={config.max}
                  step={config.step}
                />
              ))}

              {/* 文本对齐 */}
              {renderToggleButtonGroup(
                t("text_align"),
                textStyle.textAlign,
                handleAlignmentChange,
                [
                  {
                    value: "left",
                    icon: <FormatAlignLeftIcon fontSize="small" />,
                    label: "left aligned",
                  },
                  {
                    value: "center",
                    icon: <FormatAlignCenterIcon fontSize="small" />,
                    label: "centered",
                  },
                  {
                    value: "right",
                    icon: <FormatAlignRightIcon fontSize="small" />,
                    label: "right aligned",
                  },
                ]
              )}

              <Divider />

              {/* 文本样式 */}
              {renderToggleButtonGroup(
                t("styles"),
                textStyle.styles,
                handleStyleChange,
                [
                  {
                    value: "bold",
                    icon: <FormatBoldIcon fontSize="small" />,
                    label: "bold",
                  },
                  {
                    value: "italic",
                    icon: <FormatItalicIcon fontSize="small" />,
                    label: "italic",
                  },
                  {
                    value: "underlined",
                    icon: <FormatUnderlinedIcon fontSize="small" />,
                    label: "underlined",
                  },
                  {
                    value: "strikethrough",
                    icon: <StrikethroughSIcon fontSize="small" />,
                    label: "strikethrough",
                  },
                ]
              )}

              <Divider />
              <BaseSetting element={textElement} />
            </>
          )}

          {activeTab === 1 && (
            <>
              {/* 高级滑块控件 */}
              {advancedSliderConfigs.map((config) => (
                <SliderWithInput
                  key={config.key}
                  label={config.label}
                  value={config.value}
                  onChange={(newValue) =>
                    setTextStyle((prev) => ({
                      ...prev,
                      [config.key]: newValue,
                    }))
                  }
                  onChangeCommitted={(newValue) =>
                    updateTextStyleAndStore({ [config.key]: newValue })
                  }
                  min={config.min}
                  max={config.max}
                  step={config.step}
                />
              ))}

              {/* 描边颜色 */}
              {renderColorRow(
                t("stroke_color"),
                textStyle.strokeColor,
                (color) => updateTextStyleAndStore({ strokeColor: color })
              )}

              <Divider />

              {/* 阴影颜色 */}
              {renderColorRow(
                t("shadow_color"),
                textStyle.shadowColor,
                (color) => updateTextStyleAndStore({ shadowColor: color })
              )}
            </>
          )}
        </Stack>
      </Box>
    </Box>
  );
};

export default observer(BasicText);
